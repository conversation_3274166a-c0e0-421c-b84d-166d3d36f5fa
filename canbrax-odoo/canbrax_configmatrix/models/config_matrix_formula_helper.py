# -*- coding: utf-8 -*-

import math
import logging
import re
import json
from odoo import models, api
from odoo.tools.safe_eval import safe_eval

_logger = logging.getLogger(__name__)


class ConfigMatrixFormulaHelper(models.AbstractModel):
    """
    Helper Mixin for JavaScript-to-Python Formula Conversion and Evaluation

    This mixin provides centralized functionality for:
    1. Converting JavaScript formulas to Python syntax
    2. Building safe evaluation contexts with math functions
    3. Evaluating formulas with configuration values
    4. Handling common evaluation patterns
    5. Field value normalization for consistent data types

    Usage:
        class MyModel(models.Model):
            _name = 'my.model'
            _inherit = ['config.matrix.formula.helper']

            def my_method(self):
                result = self.evaluate_formula('field1 + field2', {'field1': 10, 'field2': 20})
                normalized = self.normalize_field_values(field_values, template_id)
    """
    _name = 'config.matrix.formula.helper'
    _description = 'Formula Helper Mixin for ConfigMatrix'

    def convert_js_to_python(self, js_formula):
        """
        Convert JavaScript formula syntax to Python syntax
        
        Args:
            js_formula (str): JavaScript formula to convert
            
        Returns:
            str: Python-compatible formula
        """
        if not js_formula:
            return js_formula

        python_formula = js_formula

        # Convert JavaScript operators to Python
        python_formula = python_formula.replace('===', '==')
        python_formula = python_formula.replace('!==', '!=')
        python_formula = python_formula.replace('&&', ' and ')
        
        # Convert JavaScript Math.min/max to Python min/max
        python_formula = python_formula.replace('Math.min(', 'min(')
        python_formula = python_formula.replace('Math.max(', 'max(')
        python_formula = python_formula.replace('Math.', '')

        # Convert JavaScript Number() function to parseFloat
        python_formula = python_formula.replace('Number(', 'parseFloat(')

        # Convert ternary operator first (before handling ||)
        python_formula = self._convert_ternary_operators(python_formula)

        # Convert JavaScript logical OR with default value (|| 0) to Python equivalent
        # This needs to be after ternary conversion to avoid breaking nested ternary logic
        python_formula = python_formula.replace('||', ' or ')

        # Convert JavaScript string concatenation to Python
        python_formula = self._convert_string_concatenation(python_formula)

        # Clean up any remaining JavaScript-specific syntax
        python_formula = python_formula.replace('===', '==')  # Double-check
        python_formula = python_formula.replace('!==', '!=')  # Double-check

        return python_formula

    def _convert_ternary_operators(self, formula):
        """Convert JavaScript ternary operators to Python if-else expressions"""
        if not formula or '?' not in formula or ':' not in formula:
            return formula
        
        # Handle complex nested ternary operators
        def convert_nested_ternary(expr):
            """Convert nested ternary operators to Python syntax"""
            # Find the outermost ternary operator
            first_question = expr.find('?')
            if first_question == -1:
                return expr
            
            # Find the matching colon for this question mark
            # We need to handle nested ternary operators properly
            colon_pos = -1
            paren_count = 0
            question_count = 0
            
            for i, char in enumerate(expr[first_question + 1:], first_question + 1):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                elif char == '?':
                    question_count += 1
                elif char == ':' and paren_count == 0:
                    if question_count == 0:
                        colon_pos = i
                        break
                    else:
                        question_count -= 1
            
            if colon_pos == -1:
                # No matching colon found, return as is
                return expr
            
            # Extract the parts
            condition = expr[:first_question].strip()
            true_part = expr[first_question + 1:colon_pos].strip()
            false_part = expr[colon_pos + 1:].strip()
            
            # Remove outer parentheses if they exist
            if true_part.startswith('(') and true_part.endswith(')'):
                true_part = true_part[1:-1]
            if false_part.startswith('(') and false_part.endswith(')'):
                false_part = false_part[1:-1]
            
            # Check if false_part contains another ternary operator
            if '?' in false_part and ':' in false_part:
                # Recursively convert the nested ternary
                false_part = convert_nested_ternary(false_part)
            
            # Convert to Python if-else
            python_expr = f"({true_part} if ({condition}) else {false_part})"
            return python_expr
        
        try:
            return convert_nested_ternary(formula)
        except Exception as e:
            _logger.warning(f"Ternary conversion failed for formula: {formula[:100]}... Error: {e}")
            # Fallback: simple replacement (may not work for complex cases)
            return formula.replace(' ? ', ' if ').replace(' : ', ' else ')

    def _convert_string_concatenation(self, formula):
        """Convert JavaScript string concatenation to Python string formatting"""
        import re
        
        # More comprehensive approach: find all variables that are being concatenated
        # and wrap them with str()
        
        # First, let's find all variable names in the formula
        variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
        variables = re.findall(variable_pattern, formula)
        
        # For each variable, check if it's being concatenated with a string
        for variable in variables:
            # Skip if it's already wrapped with str()
            if f'str({variable})' in formula:
                continue
                
            # Check if this variable is being concatenated with a string
            # Pattern: variable + 'string' or 'string' + variable
            concat_pattern = rf'\b{variable}\b\s*\+\s*[\'"][^\'"]*[\'"]|[\'"][^\'"]*[\'"]\s*\+\s*\b{variable}\b'
            
            if re.search(concat_pattern, formula):
                # Replace the variable with str(variable)
                formula = re.sub(rf'\b{variable}\b', f'str({variable})', formula)
        
        return formula

    def build_evaluation_context(self, configuration_values=None, include_math=True, include_js_functions=True, template_id=None):
        """
        Build a safe evaluation context for formula evaluation

        Args:
            configuration_values (dict): Configuration field values
            include_math (bool): Include math functions in context
            include_js_functions (bool): Include JavaScript-compatible functions
            template_id (int): Template ID for field normalization (optional)

        Returns:
            dict: Safe evaluation context
        """
        context = {}

        # Add configuration values if provided
        if configuration_values:
            # Normalize field values if template_id is provided
            if template_id:
                normalized_values = self.normalize_field_values(configuration_values, template_id)
                context.update(normalized_values)
            else:
                # Add all values as-is if no template for normalization
                context.update(configuration_values)
        
        # Add boolean context
        bool_context = {
            'true': True,
            'false': False,
            'True': True,
            'False': False,
        }
        context.update(bool_context)
        
        # Add math functions if requested
        if include_math:
            math_context = {
                'round': round,
                'ceil': math.ceil,
                'floor': math.floor,
                'abs': abs,
                'max': max,
                'min': min,
                'sum': sum,
                'sqrt': math.sqrt,
                'pow': math.pow,
                'log': math.log,
                'log10': math.log10,
                'exp': math.exp,
                'sin': math.sin,
                'cos': math.cos,
                'tan': math.tan,
                'pi': math.pi,
                'e': math.e,
            }
            context.update(math_context)
        
        # Add JavaScript-compatible functions if requested
        if include_js_functions:
            def parseFloat(value):
                """JavaScript parseFloat equivalent"""
                if value is None or value == '':
                    return 0
                try:
                    return float(str(value))
                except:
                    return 0

            def parseInt(value):
                """JavaScript parseInt equivalent"""
                if value is None or value == '':
                    return 0
                try:
                    return int(float(str(value)))
                except:
                    return 0

            js_context = {
                'parseFloat': parseFloat,
                'parseInt': parseInt,
                'Number': parseFloat,  # JavaScript Number() equivalent
                'Math': type('Math', (), {
                    'max': max, 'min': min, 'abs': abs, 'ceil': math.ceil,
                    'floor': math.floor, 'sqrt': math.sqrt
                })()
            }
            context.update(js_context)
        
        return context

    def evaluate_formula(self, formula, configuration_values=None, default_value=None,
                        include_math=True, include_js_functions=True, template_id=None):
        """
        Evaluate a JavaScript formula with configuration values

        Args:
            formula (str): JavaScript formula to evaluate
            configuration_values (dict): Configuration field values
            default_value: Default value if evaluation fails
            include_math (bool): Include math functions in context
            include_js_functions (bool): Include JavaScript-compatible functions
            template_id (int): Template ID for field normalization (optional)

        Returns:
            The result of formula evaluation or default_value if evaluation fails
        """
        if not formula:
            return default_value

        try:
            # Convert JavaScript to Python
            python_formula = self.convert_js_to_python(formula)

            # Build evaluation context with optional normalization
            context = self.build_evaluation_context(
                configuration_values,
                include_math=include_math,
                include_js_functions=include_js_functions,
                template_id=template_id
            )

            # Evaluate the formula
            result = safe_eval(python_formula, context)
            return result

        except Exception as e:
            _logger.error(f"Error evaluating formula '{formula}': {str(e)}")
            return default_value

    def evaluate_condition(self, condition, configuration_values=None, default_result=True, template_id=None):
        """
        Evaluate a condition formula (returns boolean)

        Args:
            condition (str): JavaScript condition to evaluate
            configuration_values (dict): Configuration field values
            default_result (bool): Default result if evaluation fails
            template_id (int): Template ID for field normalization (optional)

        Returns:
            bool: True if condition is met, False otherwise
        """
        if not condition or condition.strip() == 'true':
            return True

        try:
            # Convert JavaScript to Python
            python_condition = self.convert_js_to_python(condition)

            # Build evaluation context with optional normalization
            context = self.build_evaluation_context(configuration_values, template_id=template_id)

            # Evaluate the condition
            result = safe_eval(python_condition, context)
            return bool(result)

        except Exception as e:
            _logger.warning(f"Error evaluating condition '{condition}': {e}")
            return default_result

    def evaluate_visibility_condition(self, condition, field_values=None, default_result=True, template_id=None):
        """
        Evaluate visibility condition with JSON marker handling

        Args:
            condition (str): Visibility condition (may include JSON markers)
            field_values (dict): Field values for evaluation
            default_result (bool): Default result if evaluation fails
            template_id (int): Template ID for field normalization (optional)

        Returns:
            bool: True if field should be visible, False otherwise
        """
        if not condition:
            return True
        
        # Check if this is a JSON condition marker
        if condition.startswith('__JSON__'):
            try:
                # Extract the JSON part after the marker
                json_str = condition[8:]  # Remove '__JSON__' prefix
                import json
                condition_data = json.loads(json_str)
                
                # Evaluate each condition and apply the logic
                result = True
                for i, item in enumerate(condition_data):
                    cond_expr = item['condition']
                    logic_op = item['logic']
                    
                    # Convert JavaScript syntax to Python
                    cond_expr = self.convert_js_to_python(cond_expr)
                    
                    # Evaluate the individual condition
                    try:
                        # Build evaluation context with optional normalization
                        eval_context = self.build_evaluation_context(field_values, template_id=template_id)

                        cond_result = safe_eval(cond_expr, eval_context)
                        
                        if i == 0:
                            result = cond_result
                        else:
                            if logic_op == 'and':
                                result = result and cond_result
                            elif logic_op == 'or':
                                result = result or cond_result
                            elif logic_op == 'not':
                                result = not cond_result
                            else:
                                # Default to 'and' if unknown logic operator
                                result = result and cond_result
                    except Exception as e:
                        _logger.warning(f"Error evaluating condition '{cond_expr}' (original: '{item['condition']}'): {e}")
                        # If we can't evaluate a condition, default to False for safety
                        result = False
                        break
                
                return result
                
            except (json.JSONDecodeError, Exception) as e:
                _logger.error(f"Error parsing JSON condition '{condition}': {e}")
                # Fallback to always visible if JSON parsing fails
                return True
        
        # Handle regular string conditions
        return self.evaluate_condition(condition, field_values, default_result, template_id)

    def calculate_component_quantity(self, formula, config_values, default=1.0, template_id=None):
        """
        Calculate component quantity using formula

        Args:
            formula (str): JavaScript formula for quantity calculation
            config_values (dict): Configuration values
            default (float): Default quantity if calculation fails
            template_id (int): Template ID for field normalization (optional)

        Returns:
            float: Calculated quantity
        """
        if not formula:
            return default

        try:
            # Convert JavaScript to Python
            python_formula = self.convert_js_to_python(formula)

            # Build evaluation context with optional normalization
            context = self.build_evaluation_context(config_values, template_id=template_id)

            # Evaluate formula
            qty = safe_eval(python_formula, context)
            return float(qty)

        except Exception as e:
            _logger.error(f"Error evaluating quantity formula '{formula}': {str(e)}")
            return default

    def validate_formula_syntax(self, formula):
        """
        Validate that a formula can be converted and evaluated
        
        Args:
            formula (str): JavaScript formula to validate
            
        Returns:
            dict: Validation result with status and details
        """
        if not formula:
            return {
                'status': 'error',
                'message': 'Formula is empty',
                'python_formula': None,
                'error': None
            }
        
        try:
            # Try to convert JavaScript to Python
            python_formula = self.convert_js_to_python(formula)
            
            # Test with sample values
            test_context = self.build_evaluation_context({
                'test_value': 100,
                'another_value': 50,
                '_CALCULATED_test': 200
            })
            
            # Try to evaluate the formula
            try:
                result = safe_eval(python_formula, test_context)
                return {
                    'status': 'valid',
                    'python_formula': python_formula,
                    'test_result': result,
                    'error': None
                }
            except Exception as eval_error:
                return {
                    'status': 'evaluation_error',
                    'python_formula': python_formula,
                    'test_result': None,
                    'error': str(eval_error)
                }
                
        except Exception as conversion_error:
            return {
                'status': 'conversion_error',
                'python_formula': None,
                'test_result': None,
                'error': str(conversion_error)
            }

    def evaluate_formula_with_custom_context(self, formula, configuration_values=None, custom_functions=None, default_value=None, template_id=None):
        """
        Evaluate a JavaScript formula with custom functions in the context

        Args:
            formula (str): JavaScript formula to evaluate
            configuration_values (dict): Configuration field values
            custom_functions (dict): Custom functions to add to the context
            default_value: Default value if evaluation fails
            template_id (int): Template ID for field normalization (optional)

        Returns:
            The result of formula evaluation or default_value if evaluation fails
        """
        if not formula:
            return default_value

        try:
            # Convert JavaScript to Python
            python_formula = self.convert_js_to_python(formula)

            # Build evaluation context with optional normalization
            context = self.build_evaluation_context(
                configuration_values,
                include_math=True,
                include_js_functions=True,
                template_id=template_id
            )

            # Add custom functions if provided
            if custom_functions:
                context.update(custom_functions)

            # Evaluate the formula
            result = safe_eval(python_formula, context)
            return result

        except Exception as e:
            _logger.error(f"Error evaluating formula '{formula}' with custom context: {str(e)}")
            return default_value

    # ========================================
    # FIELD VALUE NORMALIZATION METHODS
    # ========================================

    def normalize_field_values(self, field_values, template_id, context_info=None):
        """
        Normalize field values for template calculations

        Args:
            field_values (dict): Raw field values from UI or backend
            template_id (int): Template ID for field type lookup
            context_info (dict): Context information (UI vs backend, source, etc.)

        Returns:
            dict: Normalized field values with consistent data types
        """
        if not field_values:
            return {}

        if not template_id:
            return field_values.copy()

        try:
            # Get field type mapping from template
            field_type_mapping = self._get_field_type_mapping(template_id)

            # Normalize field values
            normalized_values = {}
            conversion_summary = {}

            for field_key, raw_value in field_values.items():
                try:
                    # Skip calculated fields - they're already processed
                    if field_key.startswith('_CALCULATED_'):
                        normalized_values[field_key] = raw_value
                        continue

                    # Get field type
                    field_type = field_type_mapping.get(field_key, 'text')  # Default to text

                    # Normalize value based on type
                    normalized_value = self._normalize_single_value(raw_value, field_type, field_key)
                    normalized_values[field_key] = normalized_value

                    # Track conversions for debugging
                    if raw_value != normalized_value:
                        conversion_summary[field_key] = {
                            'from': raw_value,
                            'to': normalized_value,
                            'type': field_type
                        }

                except Exception as e:
                    # Fallback: keep original value
                    normalized_values[field_key] = raw_value

            return normalized_values

        except Exception as e:
            return field_values.copy()

    def normalize_for_template_calculation(self, field_values, template_id, context_type='unknown'):
        """
        Convenience method for template calculations

        Args:
            field_values (dict): Raw field values
            template_id (int): Template ID
            context_type (str): Context type (UI_FIELD_OPTION_MAPPING, BACKEND_SAVE_CONFIG, etc.)

        Returns:
            dict: Normalized field values ready for template calculation
        """
        context_info = {
            'source': 'template_calculation',
            'context_type': context_type
        }

        return self.normalize_field_values(field_values, template_id, context_info)

    def _get_field_type_mapping(self, template_id):
        """
        Get field type mapping for normalization

        Args:
            template_id (int): Template ID

        Returns:
            dict: Mapping of field keys to field types
        """
        try:
            template = self.env['config.matrix.template'].browse(template_id)
            if not template.exists():
                return {}

            field_mapping = {}

            for section in template.section_ids:
                for field in section.field_ids:
                    # Map both field ID and technical name to field type
                    field_mapping[str(field.id)] = field.field_type
                    if field.technical_name:
                        field_mapping[field.technical_name] = field.field_type

            return field_mapping

        except Exception as e:
            return {}

    def _normalize_single_value(self, value, field_type, field_key):
        """
        Normalize a single field value based on its type

        Args:
            value: Raw value to normalize
            field_type (str): Expected field type
            field_key (str): Field key for debugging

        Returns:
            Normalized value
        """
        # Handle None and empty values
        if value is None:
            return self._get_default_value(field_type)

        # Handle empty strings
        if isinstance(value, str) and value.strip() == '':
            return self._get_default_value(field_type)

        try:
            if field_type == 'integer':
                return self._normalize_integer(value)
            elif field_type == 'float':
                return self._normalize_float(value)
            elif field_type == 'boolean':
                return self._normalize_boolean(value)
            elif field_type == 'selection':
                return self._normalize_selection(value)
            elif field_type in ['text', 'char']:
                return self._normalize_text(value)
            else:
                # Unknown type, try to preserve as-is
                return value

        except Exception as e:
            return self._get_default_value(field_type)

    def _normalize_integer(self, value):
        """Normalize value to integer"""
        if isinstance(value, int):
            return value
        if isinstance(value, float):
            return int(value)
        if isinstance(value, str):
            # Try direct conversion
            if value.isdigit():
                return int(value)
            # Try to extract number from string like "3 per door"
            match = re.search(r'^(\d+)', value.strip())
            if match:
                return int(match.group(1))
        return 0

    def _normalize_float(self, value):
        """Normalize value to float"""
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # Try direct conversion
            try:
                return float(value)
            except ValueError:
                # Try to extract number from string
                match = re.search(r'^(\d+\.?\d*)', value.strip())
                if match:
                    return float(match.group(1))
        return 0.0

    def _normalize_boolean(self, value):
        """Normalize value to boolean"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ['true', '1', 'yes', 'on', 'checked']
        if isinstance(value, (int, float)):
            return bool(value)
        return False

    def _normalize_selection(self, value):
        """Normalize selection field value"""
        # For selection fields, we typically want to preserve the string value
        # but also handle cases where numeric extraction is needed
        if isinstance(value, str):
            return value.strip()
        return str(value) if value is not None else ''

    def _normalize_text(self, value):
        """Normalize text field value"""
        if isinstance(value, str):
            return value.strip()
        return str(value) if value is not None else ''

    def _get_default_value(self, field_type):
        """Get default value for field type"""
        defaults = {
            'integer': 0,
            'float': 0.0,
            'boolean': False,
            'selection': '',
            'text': '',
            'char': ''
        }
        return defaults.get(field_type, '')
