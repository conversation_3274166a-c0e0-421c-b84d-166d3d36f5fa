# -*- coding: utf-8 -*-

from odoo import models, fields, api
import json
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixCalculatedField(models.Model):
    _name = 'config.matrix.calculated.field'
    _inherit = ['config.matrix.formula.helper']
    _description = 'Calculated Field Definition'
    _order = 'sequence, name'

    name = fields.Char("Field Name", required=True, help="The calculated field name (e.g., _CALCULATED_smallest_door_height)")
    description = fields.Text("Description", help="Description of what this field calculates")
    formula = fields.Text("Formula", required=True, help="JavaScript expression to calculate the field value")
    sequence = fields.Integer("Sequence", default=10, help="Order of calculation (lower numbers calculated first)")
    active = fields.Boolean("Active", default=True)
    
    # Categorization
    category = fields.Selection([
        ('input', 'Input Fields'),
        ('basic', 'Basic Derived Values'),
        ('threshold', 'Height Thresholds'),
        ('range', 'Range Validity'),
        ('condition', 'Lock Height Conditions'),
        ('composite', 'Composite Conditions'),
        ('kit', 'Kit Results'),
        ('final', 'Final Results')
    ], string="Category", default='basic', help="Category for organization")
    
    # Dependencies
    depends_on = fields.Text("Depends On", help="Comma-separated list of field names this calculation depends on")
    
    # Template association (Many2Many)
    template_ids = fields.Many2many('config.matrix.template', 'calc_field_template_rel',
                                   'calc_field_id', 'template_id',
                                   string="Templates", help="Templates this field applies to (leave empty for global)")
    
    # Data type
    data_type = fields.Selection([
        ('number', 'Number'),
        ('boolean', 'Boolean'),
        ('string', 'String')
    ], string="Data Type", default='number', help="Expected data type of the result")

    

    @api.constrains('name')
    def _check_name_format(self):
        for record in self:
            if not record.name.startswith('_CALCULATED_'):
                raise ValueError("Calculated field names must start with '_CALCULATED_'")

    def action_load_configuration_data(self):
        """Load data from selected configuration"""
        self.ensure_one()
        if not self.test_configuration_id:
            return

        try:
            # Load configuration data
            config_data = json.loads(self.test_configuration_id.config_data or '{}')

            # Convert field IDs to technical names for better readability
            converted_data = self._convert_field_ids_to_technical_names(config_data)

            # Format as pretty JSON
            self.test_values = json.dumps(converted_data, indent=2, sort_keys=True)
            self.test_error = False

        except json.JSONDecodeError as e:
            self.test_error = f"Invalid configuration data: {str(e)}"
        except Exception as e:
            self.test_error = f"Error loading configuration: {str(e)}"

    def _convert_field_ids_to_technical_names(self, config_data):
        """Convert field IDs to technical names for better readability"""
        if not config_data:
            return {}

        converted_data = {}

        # Get the template from the test configuration
        template = self.template_ids[0] if self.template_ids else None
        if not template:
            # If no template, return original data
            return config_data

        # Create a mapping of field IDs to technical names
        field_mapping = {}
        for section in template.section_ids:
            for field in section.field_ids:
                field_mapping[str(field.id)] = field.technical_name

        # Convert the data
        for key, value in config_data.items():
            if key in field_mapping:
                # Use technical name instead of field ID
                technical_name = field_mapping[key]
                converted_data[technical_name] = value
                print(f"🔄 Converted field ID {key} → {technical_name} = {value}")
            else:
                # Keep original key if no mapping found
                converted_data[key] = value

        return converted_data



    @api.model
    def convert_js_to_python(self, js_formula):
        """Class method to convert JavaScript to Python syntax - can be called from other models"""
        if not js_formula:
            return js_formula

        # Use inherited formula helper method for conversion
        return super().convert_js_to_python(js_formula)

    @api.model
    def get_calculated_fields_for_template(self, template_id=None):
        """Get all calculated fields for a template, ordered by sequence"""
        domain = [('active', '=', True)]
        if template_id:
            domain.append('|')
            domain.append(('template_ids', 'in', [template_id]))
            domain.append(('template_ids', '=', False))
        else:
            domain.append(('template_ids', '=', False))

        fields = self.search(domain, order='sequence, name')
        _logger.info(f"[MESH_DEBUG] Found {len(fields)} calculated fields for template {template_id}")

        # Log mesh-related fields specifically
        mesh_fields = fields.filtered(lambda f: 'mesh' in f.name.lower())
        _logger.info(f"[MESH_DEBUG] Mesh-related fields found: {[f.name for f in mesh_fields]}")
        for field in mesh_fields:
            _logger.info(f"[MESH_DEBUG] Field {field.name}: formula='{field.formula}', depends_on='{field.depends_on}', active={field.active}")

        # Check if each field is used as height_calculated_field_id or width_calculated_field_id in any price matrix for the given template_id
        PriceMatrix = self.env['config.matrix.price.matrix']
        height_field_ids = []
        width_field_ids = []
        if template_id:
            price_matrices = PriceMatrix.sudo().search([('matrix_id', '=', template_id), ('active', '=', True)])
            height_field_ids = price_matrices.mapped('height_calculated_field_id.id')
            width_field_ids = price_matrices.mapped('width_calculated_field_id.id')
        # Return field data for JavaScript
        return [{
            'name': field.name,
            'formula': field.formula,
            'description': field.description,
            'sequence': field.sequence,
            'category': field.category,
            'data_type': field.data_type,
            'is_pricing_matrix': field.id in height_field_ids or field.id in width_field_ids
        } for field in fields]

    @api.model
    def _get_math_context(self, template=None, context=None):
        """Get math functions context for formula evaluation"""
        import math
        
        # Helper function to find midrail height fields
        def find_midrail_height():
            """Find any field related to midrail height with a value > 0"""
            import logging
            _logger = logging.getLogger(__name__)

            _logger.info("[MIDRAIL-DEBUG] find_midrail_height() function called!")

            # Access the current context (field_values + calculated results)
            current_context = context or {}

            # Debug: Show all numeric fields in context
            numeric_fields = {k: v for k, v in current_context.items() if isinstance(v, (int, float)) and v > 0}
            _logger.info(f"[MIDRAIL-DEBUG] All numeric fields in context: {numeric_fields}")

            # Debug: Show all fields containing 'midrail'
            midrail_fields = {k: v for k, v in current_context.items() if 'midrail' in str(k).lower()}
            _logger.info(f"[MIDRAIL-DEBUG] All midrail-related fields: {midrail_fields}")

            # Search patterns for midrail height fields (more flexible)
            search_patterns = [
                'midrail_height_mm',  # Matches bx_dbl_hinge_midrail_height_mm_case1
                'midrail_height',
                'mid_rail',
                'rail_height',
                'top_of_midrail',
                'midrail_position'
            ]

            # First pass: look in calculated fields (technical names)
            # _logger.info(f"[MIDRAIL-DEBUG] Searching in context with {len(current_context)} items")
            for field_name, value in current_context.items():
                if isinstance(value, (int, float)) and value > 0:
                    field_lower = field_name.lower()
                    # _logger.info(f"[MIDRAIL-DEBUG] Checking field: {field_name} = {value}")

                    # Skip boolean case fields - they don't contain height values
                    if 'case' in field_lower and isinstance(value, bool):
                        _logger.info(f"[MIDRAIL-DEBUG] Skipping boolean case field: {field_name}")
                        continue

                    for pattern in search_patterns:
                        if pattern in field_lower:
                            # Additional check: make sure this looks like a height value (not just a boolean)
                            if isinstance(value, bool):
                                # _logger.info(f"[MIDRAIL-DEBUG] Skipping boolean field: {field_name} = {value}")
                                continue
                            # _logger.info(f"[MIDRAIL-DEBUG] ✅ Found midrail field in calculated: {field_name} = {value}")
                            return value

            # Second pass: look in original field_values (field IDs) by checking field names
            if template:
                # Search through template fields for midrail-related fields
                for section in template.section_ids:
                    for field in section.field_ids:
                        field_name_lower = field.name.lower()
                        field_tech_lower = (field.technical_name or '').lower()

                        # Check if this field matches midrail patterns
                        is_midrail_field = False
                        for pattern in search_patterns:
                            if pattern in field_name_lower or pattern in field_tech_lower:
                                is_midrail_field = True
                                break

                        if is_midrail_field:
                            # Check if this field has a value in the context (which contains field_values)
                            field_value = current_context.get(str(field.id))
                            if field_value and isinstance(field_value, (int, float, str)):
                                try:
                                    numeric_value = float(field_value)
                                    if numeric_value > 0:
                                        _logger.info(f"[MIDRAIL-DEBUG] Found midrail field in config: {field.name} (ID: {field.id}) = {numeric_value}")
                                        return numeric_value
                                except (ValueError, TypeError):
                                    continue

            # Log all available fields for debugging
            numeric_fields = {k: v for k, v in current_context.items() if isinstance(v, (int, float)) and v > 0}
            _logger.info(f"[MIDRAIL-DEBUG] Available calculated fields: {numeric_fields}")

            # Check if any midrail case is true but no height found
            midrail_case1 = current_context.get('_CALCULATED_midrail_case1', False)
            midrail_case2 = current_context.get('_CALCULATED_midrail_case2', False)

            if midrail_case1 or midrail_case2:
                _logger.info(f"[MIDRAIL-DEBUG] Midrail case detected (case1={midrail_case1}, case2={midrail_case2}) but no height field found")
                # For small doors, even if case2 is true, we shouldn't have a midrail
                door_height = current_context.get('_CALCULATED_largest_door_height', 0)
                door_width = current_context.get('_CALCULATED_largest_door_width', 0)

                if door_height < 1000 or door_width < 1000:
                    _logger.info(f"[MIDRAIL-DEBUG] Door too small ({door_width}x{door_height}mm) - no midrail needed")
                    return 0

                # For larger doors, use a default midrail position
                if door_height > 0:
                    default_position = door_height * 0.6  # 60% of door height
                    _logger.info(f"[MIDRAIL-DEBUG] Using default midrail position: {default_position}")
                    return default_position

            _logger.info(f"[MIDRAIL-DEBUG] No midrail required")
            return 0

        # JavaScript-compatible functions
        def parseFloat(value):
            """JavaScript parseFloat equivalent"""
            if value is None or value == '':
                return 0
            try:
                return float(str(value))
            except:
                return 0

        def parseInt(value):
            """JavaScript parseInt equivalent"""
            if value is None or value == '':
                return 0
            try:
                return int(float(str(value)))
            except:
                return 0

        return {
            'min': min,
            'max': max,
            'abs': abs,
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'sqrt': math.sqrt,
            'pow': math.pow,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'pi': math.pi,
            'e': math.e,
            'find_midrail_height': find_midrail_height,
            'parseFloat': parseFloat,
            'parseInt': parseInt,
            'Number': parseFloat,  # JavaScript Number() equivalent
        }

    @api.model
    def _sort_fields_by_dependencies(self, calculated_fields):
        """Sort fields by dependencies using improved topological sort with sequence consideration"""
        # Create dependency graph and field info
        dependency_graph = {}
        field_info = {}
        field_sequences = {}
        
        for field in calculated_fields:
            field_name = field['name']
            formula = field['formula']
            sequence = field.get('sequence', 10)
            
            # Extract dependencies from formula
            dependencies = self._extract_dependencies_from_formula(formula)
            
            # Filter to only include calculated field dependencies
            calculated_dependencies = [dep for dep in dependencies if dep.startswith('_CALCULATED_')]
            
            dependency_graph[field_name] = calculated_dependencies
            field_info[field_name] = field
            field_sequences[field_name] = sequence
        
        # Log dependency graph for debugging
        _logger.info(f"[DEPENDENCY_DEBUG] Dependency graph: {dependency_graph}")
        
        # Perform improved topological sort with sequence consideration
        try:
            sorted_field_names = self._topological_sort_with_sequence(dependency_graph, field_sequences)
            _logger.info(f"[DEPENDENCY_DEBUG] Improved topological sort successful: {sorted_field_names}")
        except Exception as e:
            _logger.warning(f"[DEPENDENCY_DEBUG] Circular dependency detected: {e}")
            # Fall back to sequence-based ordering
            sorted_field_names = [field['name'] for field in sorted(calculated_fields, key=lambda x: x.get('sequence', 10))]
            _logger.info(f"[DEPENDENCY_DEBUG] Using sequence-based fallback: {sorted_field_names}")
        
        # Return fields in dependency order
        sorted_fields = []
        for field_name in sorted_field_names:
            if field_name in field_info:
                sorted_fields.append(field_info[field_name])
        
        return sorted_fields

    @api.model
    def _can_calculate_field(self, field, context, results):
        """Check if a field can be calculated (all dependencies available)"""
        field_name = field['name']
        formula = field['formula']
        
        # Extract dependencies from formula
        dependencies = self._extract_dependencies_from_formula(formula)
        
        # Check if all dependencies are available
        for dep in dependencies:
            if dep not in context and dep not in results:
                _logger.debug(f"[DEPENDENCY_DEBUG] Cannot calculate {field_name}: missing dependency {dep}")
                return False
        
        return True

    @api.model
    def _evaluate_field(self, field, context, results, template_id=None):
        """Safely evaluate a field formula"""
        field_name = field['name']
        formula = field['formula']

        # Special handling for _CALCULATED_midrail_height
        if field_name == '_CALCULATED_midrail_height':
            return context['find_midrail_height']()

        # Convert JavaScript formula to Python syntax
        python_formula = self.convert_js_to_python(formula)

        # Debug logging for mesh series formula conversion
        if field_name == '_CALCULATED_mesh_series':
            _logger.info(f"[MESH_DEBUG] Original formula: {formula}")
            _logger.info(f"[MESH_DEBUG] Converted formula: {python_formula}")
            _logger.info(f"[MESH_DEBUG] Context for mesh_series calculation: mesh_series={context.get('mesh_series')}, template_mesh_series={context.get('template_mesh_series')}, _CALCULATED_mesh_required={context.get('_CALCULATED_mesh_required')}")
            _logger.info(f"[MESH_DEBUG] About to evaluate: {python_formula}")

        # Ensure values are properly converted based on their data types before evaluation
        context = self._ensure_proper_types(context)

        # Use the formula helper for evaluation with automatic normalization if template_id provided
        result = self.evaluate_formula(formula, context, template_id=template_id)
        # Special debug logging for mesh-related fields
        # if 'mesh' in field_name.lower():
        #     _logger.info(f"[MESH_DEBUG] Calculated {field_name} = {result}")
        #     if field_name == '_CALCULATED_mesh_series':
        #         _logger.info(f"[MESH_DEBUG] Context for mesh_series calculation:")
        #         _logger.info(f"[MESH_DEBUG]   mesh_series = {context.get('mesh_series')}")
        #         _logger.info(f"[MESH_DEBUG]   template_mesh_series = {context.get('template_mesh_series')}")
        #         _logger.info(f"[MESH_DEBUG]   _CALCULATED_mesh_required = {context.get('_CALCULATED_mesh_required')}")
        #         _logger.info(f"[MESH_DEBUG]   Formula: {formula}")
        
        return result

    @api.model
    def _ensure_proper_types(self, context):
        """Ensure that calculated field values are properly converted based on their data type"""
        converted_context = dict(context)
        
        for key, value in context.items():
            if key.startswith('_CALCULATED_') and value is not None:
                # Get the field's data type from the calculated field definition
                field_data_type = self._get_field_data_type(key)
                
                if field_data_type == 'number':
                    # Convert to numeric if it's a string that looks like a number
                    if isinstance(value, str):
                        try:
                            # Try to convert to float first
                            numeric_value = float(value)
                            converted_context[key] = numeric_value
                            _logger.debug(f"[TYPE_CONVERSION] Converted {key} from '{value}' to {numeric_value} (number)")
                        except (ValueError, TypeError):
                            # If conversion fails, keep as string
                            _logger.debug(f"[TYPE_CONVERSION] Keeping {key} as string: '{value}' (conversion failed)")
                    elif isinstance(value, (int, float)):
                        # Already numeric, ensure it's the right type
                        converted_context[key] = float(value)
                elif field_data_type == 'boolean':
                    # Convert to boolean
                    if isinstance(value, str):
                        if value.lower() in ('true', 'yes', '1', 'on'):
                            converted_context[key] = True
                        elif value.lower() in ('false', 'no', '0', 'off', ''):
                            converted_context[key] = False
                        else:
                            # Try to convert to number first, then to boolean
                            try:
                                numeric_value = float(value)
                                converted_context[key] = bool(numeric_value)
                            except (ValueError, TypeError):
                                converted_context[key] = bool(value)
                    else:
                        converted_context[key] = bool(value)
                    _logger.debug(f"[TYPE_CONVERSION] Converted {key} to boolean: {converted_context[key]}")
                elif field_data_type == 'string':
                    # Ensure it's a string
                    converted_context[key] = str(value)
                    _logger.debug(f"[TYPE_CONVERSION] Converted {key} to string: '{converted_context[key]}'")
                else:
                    # Unknown data type, keep as is
                    converted_context[key] = value
        
        return converted_context

    @api.model
    def _get_field_data_type(self, field_name):
        """Get the data type for a calculated field"""
        # Search for the field in the database
        field = self.search([('name', '=', field_name)], limit=1)
        if field:
            return field.data_type
        return 'number'  # Default to number if not found

    @api.model
    def calculate_fields(self, field_values, template_id=None):
        """Calculate all calculated fields for given input values with dependency resolution"""
        calculated_fields = self.get_calculated_fields_for_template(template_id)
        results = {}
        
        # Create evaluation context
        context = dict(field_values)

        # Get template for field lookups
        template = None
        if template_id:
            template = self.env['config.matrix.template'].browse(template_id)
            _logger.info(f"[MESH_DEBUG] Template loaded: {template.name}, mesh_series: {template.mesh_series}")
            # Add template mesh series to context for calculations
            if template.mesh_series:
                context['template_mesh_series'] = template.mesh_series
                _logger.info(f"[MESH_DEBUG] Added template_mesh_series to context: {template.mesh_series} for template {template.name}")
            else:
                _logger.info(f"[MESH_DEBUG] No mesh_series found on template {template.name}")
                # Add empty value to context to avoid undefined variable errors
                context['template_mesh_series'] = ''
        else:
            _logger.info(f"[MESH_DEBUG] No template_id provided")
            context['template_mesh_series'] = ''

        # CRITICAL: Ensure mesh_series is defined to prevent NameError
        if 'mesh_series' not in context:
            context['mesh_series'] = ''  # Empty string instead of None
            _logger.info(f"[MESH_DEBUG] Added mesh_series='' to context to prevent NameError")

        # Add math functions and custom functions
        context.update(self._get_math_context(template, context))
        
        # Sort fields by dependencies using topological sort
        sorted_fields = self._sort_fields_by_dependencies(calculated_fields)
        
        # Initialize results with None for all calculated fields
        for field in sorted_fields:
            results[field['name']] = None
        
        # Multi-pass calculation with dependency awareness
        max_passes = 10
        pass_count = 0
        has_changes = True
        
        _logger.info(f"[DEPENDENCY_DEBUG] Starting calculation with {len(sorted_fields)} fields in dependency order")
        
        while has_changes and pass_count < max_passes:
            has_changes = False
            pass_count += 1
            
            _logger.debug(f"[DEPENDENCY_DEBUG] Starting calculation pass {pass_count}")
            
            for field in sorted_fields:
                field_name = field['name']
                
                # Skip if already calculated successfully
                if results[field_name] is not None:
                    continue
                
                # Check if field can be calculated (dependencies available)
                if not self._can_calculate_field(field, context, results):
                    continue
                
                try:
                    # Add previously calculated fields to context
                    context.update(results)
                    
                    # Evaluate the field with template_id for automatic normalization
                    result = self._evaluate_field(field, context, results, template_id)
                    results[field_name] = result
                    has_changes = True
                    
                    # Update context for next calculations
                    context[field_name] = result
                    
                    _logger.debug(f"[DEPENDENCY_DEBUG] Pass {pass_count}: Calculated {field_name} = {result}")
                    
                except NameError as ne:
                    # Variable not found - skip this field for now
                    if field_name == '_CALCULATED_mesh_series':
                        _logger.error(f"[MESH_DEBUG] Pass {pass_count}: NameError for {field_name}: {ne}")
                        _logger.error(f"[MESH_DEBUG] Context keys: {list(context.keys())}")
                    else:
                        _logger.debug(f"[DEPENDENCY_DEBUG] Pass {pass_count}: Skipping {field_name} - variable not found: {ne}")
                    continue
                except Exception as eval_error:
                    # Other evaluation error - log and continue
                    if field_name == '_CALCULATED_mesh_series':
                        _logger.error(f"[MESH_DEBUG] Pass {pass_count}: Error calculating {field_name}: {eval_error}")
                        _logger.error(f"[MESH_DEBUG] Formula: {field['formula']}")
                        _logger.error(f"[MESH_DEBUG] Context: {context}")
                    elif field_name == '_CALCULATED_midrail_height':
                        _logger.error(f"[MIDRAIL-DEBUG] Pass {pass_count}: Error calculating {field_name}: {eval_error}")
                        _logger.error(f"[MIDRAIL-DEBUG] Formula: {field['formula']}")
                        _logger.error(f"[MIDRAIL-DEBUG] Context keys: {list(context.keys())}")
                    else:
                        _logger.debug(f"[DEPENDENCY_DEBUG] Pass {pass_count}: Skipping {field_name} - evaluation error: {eval_error}")
                    continue
                
            _logger.debug(f"[DEPENDENCY_DEBUG] Pass {pass_count} completed. Has changes: {has_changes}")
        
        if pass_count >= max_passes:
            _logger.warning(f"[DEPENDENCY_DEBUG] Maximum calculation passes ({max_passes}) reached. Some fields may not be calculated.")
        
        # Log final results
        for field_name, result in results.items():
            if result is not None:
                if field_name == '_CALCULATED_midrail_height':
                    _logger.error(f"[MIDRAIL-DEBUG] ✅ FINAL RESULT: {field_name} = {result} (type: {type(result)})")
                else:
                    _logger.debug(f"[DEPENDENCY_DEBUG] Final result for {field_name}: {result}")
            else:
                if field_name == '_CALCULATED_midrail_height':
                    _logger.error(f"[MIDRAIL-DEBUG] ❌ FAILED: {field_name} could not be calculated after {pass_count} passes")
                else:
                    _logger.warning(f"[DEPENDENCY_DEBUG] Field {field_name} could not be calculated after {pass_count} passes")
        
        _logger.info(f"[DEPENDENCY_DEBUG] Calculation completed in {pass_count} passes. {len([r for r in results.values() if r is not None])}/{len(results)} fields calculated successfully.")
        
        return results

    def _check_dependencies_available(self, formula, context):
        """Check if all dependencies in a formula are available in the context"""
        import re
        
        # Find all variable names in the formula
        # Pattern: word characters and underscores, but not starting with number
        variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
        variables = re.findall(variable_pattern, formula)
        
        # Check if all variables are available in context
        for var in variables:
            # Skip built-in functions and constants
            if var in ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow', 
                       'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
                       'parseFloat', 'parseInt', 'Number', 'Math']:
                continue
            
            # Check if variable is available in context
            if var not in context or context[var] is None:
                return False
        
        return True

    @api.model
    def analyze_calculated_field_dependencies(self, template_id=None):
        """Analyze dependencies between calculated fields to help with debugging"""
        calculated_fields = self.get_calculated_fields_for_template(template_id)
        dependency_map = {}
        
        for field in calculated_fields:
            field_name = field['name']
            formula = field['formula']
            
            # Find dependencies in the formula
            dependencies = self._extract_dependencies_from_formula(formula)
            
            # Filter to only include calculated field dependencies
            calculated_dependencies = [dep for dep in dependencies if dep.startswith('_CALCULATED_')]
            
            dependency_map[field_name] = {
                'formula': formula,
                'sequence': field.get('sequence', 10),
                'category': field.get('category', 'basic'),
                'all_dependencies': dependencies,
                'calculated_dependencies': calculated_dependencies,
                'dependency_count': len(calculated_dependencies)
            }
        
        # Sort by dependency count and sequence
        sorted_fields = sorted(dependency_map.items(), 
                              key=lambda x: (x[1]['dependency_count'], x[1]['sequence']))
        
        return {
            'dependency_map': dependency_map,
            'sorted_fields': sorted_fields,
            'total_fields': len(calculated_fields),
            'fields_with_dependencies': len([f for f in dependency_map.values() if f['dependency_count'] > 0]),
            'fields_without_dependencies': len([f for f in dependency_map.values() if f['dependency_count'] == 0])
        }

    def _extract_dependencies_from_formula(self, formula):
        """Extract all variable dependencies from a formula"""
        import re
        
        # Remove string literals first to avoid capturing them as variables
        # This regex matches single-quoted strings, double-quoted strings, and backtick strings
        string_pattern = r"'([^'\\]|\\.)*'|\"([^\"\\]|\\.)*\"|`([^`\\]|\\.)*`"
        formula_without_strings = re.sub(string_pattern, '', formula)
        
        # Find all variable names in the formula (excluding string literals)
        variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
        variables = re.findall(variable_pattern, formula_without_strings)
        
        # Filter out built-in functions and constants
        built_ins = ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow', 
                     'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
                     'parseFloat', 'parseInt', 'Number', 'Math', 'find_midrail_height']
        
        dependencies = [var for var in variables if var not in built_ins]
        return list(set(dependencies))  # Remove duplicates

    @api.model
    def get_calculation_order_suggestion(self, template_id=None):
        """Get suggested calculation order based on dependencies"""
        analysis = self.analyze_calculated_field_dependencies(template_id)
        
        # Create a dependency graph and field sequences
        dependency_graph = {}
        field_sequences = {}
        for field_name, field_info in analysis['dependency_map'].items():
            dependency_graph[field_name] = field_info['calculated_dependencies']
            field_sequences[field_name] = field_info['sequence']
        
        # Topological sort to determine calculation order
        try:
            sorted_order = self._topological_sort_with_sequence(dependency_graph, field_sequences)
        except Exception as e:
            _logger.warning(f"Circular dependency detected: {e}")
            # Fall back to sequence-based ordering
            sorted_order = [field[0] for field in analysis['sorted_fields']]
        
        # Create suggested sequence values
        suggested_sequences = {}
        for i, field_name in enumerate(sorted_order):
            suggested_sequences[field_name] = i + 1
        
        return {
            'suggested_order': sorted_order,
            'suggested_sequences': suggested_sequences,
            'analysis': analysis
        }

    def _topological_sort(self, dependency_graph):
        """Perform topological sort on dependency graph"""
        # Kahn's algorithm for topological sorting
        in_degree = {node: 0 for node in dependency_graph}
        
        # Calculate in-degrees
        for node, dependencies in dependency_graph.items():
            for dep in dependencies:
                if dep in in_degree:
                    in_degree[dep] += 1
        
        # Find nodes with no incoming edges
        queue = [node for node, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            node = queue.pop(0)
            result.append(node)
            
            # Remove edges from this node
            for dep in dependency_graph.get(node, []):
                if dep in in_degree:
                    in_degree[dep] -= 1
                    if in_degree[dep] == 0:
                        queue.append(dep)
        
        # Check for circular dependencies
        if len(result) != len(dependency_graph):
            remaining = set(dependency_graph.keys()) - set(result)
            raise ValueError(f"Circular dependency detected in fields: {remaining}")
        
        return result

    def _topological_sort_with_sequence(self, dependency_graph, field_sequences):
        """Perform topological sort with sequence consideration for fields with same dependency level"""
        # Kahn's algorithm for topological sorting with sequence-based tie-breaking
        in_degree = {node: 0 for node in dependency_graph}
        
        # Calculate in-degrees (how many dependencies each node has)
        for node, dependencies in dependency_graph.items():
            in_degree[node] = len(dependencies)
        
        # Find nodes with no incoming edges (no dependencies), sorted by sequence
        available_nodes = [node for node, degree in in_degree.items() if degree == 0]
        available_nodes.sort(key=lambda x: field_sequences.get(x, 10))
        
        result = []
        
        while available_nodes:
            # Sort available nodes by sequence to maintain consistent ordering
            available_nodes.sort(key=lambda x: field_sequences.get(x, 10))
            node = available_nodes.pop(0)
            result.append(node)
            
            # Remove edges from this node and update available nodes
            # This means: for each node that depends on the current node, reduce its in-degree
            for other_node, other_deps in dependency_graph.items():
                if node in other_deps:
                    in_degree[other_node] -= 1
                    if in_degree[other_node] == 0:
                        available_nodes.append(other_node)
        
        # Check for circular dependencies
        if len(result) != len(dependency_graph):
            remaining = set(dependency_graph.keys()) - set(result)
            _logger.error(f"[DEPENDENCY_DEBUG] Circular dependency detected in fields: {remaining}")
            _logger.error(f"[DEPENDENCY_DEBUG] Dependency graph: {dependency_graph}")
            _logger.error(f"[DEPENDENCY_DEBUG] In-degree counts: {in_degree}")
            raise ValueError(f"Circular dependency detected in fields: {remaining}")
        
        return result

    def action_create_field(self):
        """Action to create a new calculated field"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Create Calculated Field',
            'res_model': 'config.matrix.calculated.field',
            'view_mode': 'form',
            'target': 'current',
        }

    @api.model
    def import_calculated_fields_from_json(self, json_data, template_ids=None):
        """Import calculated fields from JSON definition"""
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data

            created_count = 0
            updated_count = 0

            for field_name, field_def in data.items():
                # Check if field already exists (global fields)
                existing = self.search([
                    ('name', '=', field_name),
                    ('template_ids', '=', False)
                ], limit=1)

                # Determine category and sequence from field name
                category = 'basic'
                sequence = 10  # Default sequence

                # Set sequence based on dependencies to ensure proper calculation order
                if field_name in ['_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height',
                                '_CALCULATED_lock_height', '_CALCULATED_deduction_assistance',
                                '_CALCULATED_door_split_type']:
                    # Input fields - calculate first
                    category = 'input'
                    sequence = 1
                elif field_name in ['_CALCULATED_height_calculation_method']:
                    # Method calculation - depends on input fields
                    category = 'input'
                    sequence = 2
                elif field_name in ['_CALCULATED_smallest_door_height']:
                    # Basic derived values - depends on input fields
                    category = 'basic'
                    sequence = 3
                elif field_name in ['_CALCULATED_halfway_point']:
                    # Depends on smallest_door_height
                    category = 'basic'
                    sequence = 4
                elif 'halfway' in field_name.lower() and ('plus' in field_name or 'minus' in field_name):
                    # Depends on halfway_point
                    category = 'threshold'
                    sequence = 5
                elif 'height_minus_' in field_name or 'height_plus_' in field_name:
                    # Depends on smallest_door_height
                    category = 'threshold'
                    sequence = 6
                elif field_name.startswith('_CALCULATED_is_'):
                    # Boolean conditions - can be calculated early
                    category = 'condition'
                    sequence = 2
                elif 'formula' in field_name.lower():
                    # String formulas - calculate last
                    category = 'final'
                    sequence = 10
                elif 'range' in field_name.lower() or 'valid' in field_name.lower():
                    category = 'range'
                    sequence = 7
                elif 'kit' in field_name.lower():
                    category = 'kit'
                    sequence = 8
                elif 'FINAL' in field_name:
                    category = 'final'
                    sequence = 9

                vals = {
                    'name': field_name,
                    'description': field_def.get('description', ''),
                    'formula': field_def.get('formula', ''),
                    'category': category,
                    'sequence': sequence,
                    'template_ids': [(6, 0, template_ids)] if template_ids else False,
                    'data_type': 'boolean' if 'Boolean:' in field_def.get('description', '') else 'number',
                }

                if existing:
                    # Update existing field and add templates if specified
                    if template_ids:
                        existing_template_ids = existing.template_ids.ids
                        new_template_ids = list(set(existing_template_ids + template_ids))
                        vals['template_ids'] = [(6, 0, new_template_ids)]
                    existing.write(vals)
                    updated_count += 1
                else:
                    self.create(vals)
                    created_count += 1

            return {
                'created': created_count,
                'updated': updated_count,
                'total': len(data)
            }

        except Exception as e:
            _logger.error(f"Error importing calculated fields: {str(e)}")
            raise

    @api.model
    def debug_calculation_dependencies(self, template_id=None, test_values=None):
        """Debug method to test dependency resolution and calculation order"""
        if test_values is None:
            test_values = {}
        
        # Get dependency analysis
        analysis = self.analyze_calculated_field_dependencies(template_id)
        
        # Get suggested calculation order
        order_suggestion = self.get_calculation_order_suggestion(template_id)
        
        # Test calculation with dependency resolution
        try:
            results = self.calculate_fields(test_values, template_id)
            calculation_success = True
            error_message = None
        except Exception as e:
            calculation_success = False
            error_message = str(e)
            results = {}
        
        # Create debug report
        debug_report = {
            'template_id': template_id,
            'test_values': test_values,
            'dependency_analysis': analysis,
            'calculation_order': order_suggestion,
            'calculation_success': calculation_success,
            'error_message': error_message,
            'calculation_results': results,
            'fields_with_issues': []
        }
        
        # Identify fields that might have issues
        for field_name, field_info in analysis['dependency_map'].items():
            if field_info['dependency_count'] > 0:
                # Check if dependencies are available in test values
                missing_deps = []
                for dep in field_info['calculated_dependencies']:
                    if dep not in test_values and dep not in results:
                        missing_deps.append(dep)
                
                if missing_deps:
                    debug_report['fields_with_issues'].append({
                        'field_name': field_name,
                        'missing_dependencies': missing_deps,
                        'formula': field_info['formula'],
                        'sequence': field_info['sequence']
                    })
        
        return debug_report

    @api.model
    def validate_calculation_formulas(self, template_id=None):
        """Validate that all calculation formulas are syntactically correct"""
        calculated_fields = self.get_calculated_fields_for_template(template_id)
        validation_results = {}
        
        for field in calculated_fields:
            field_name = field['name']
            formula = field['formula']
            
            try:
                # Use the formula helper for validation
                validation_result = self.validate_formula_syntax(formula)
                
                if validation_result['status'] == 'valid':
                    validation_results[field_name] = {
                        'status': 'valid',
                        'python_formula': validation_result['python_formula'],
                        'test_result': validation_result['test_result'],
                        'error': None
                    }
                else:
                    validation_results[field_name] = {
                        'status': 'evaluation_error',
                        'python_formula': validation_result['python_formula'],
                        'test_result': None,
                        'error': validation_result['error']
                    }
                    
            except Exception as conversion_error:
                validation_results[field_name] = {
                    'status': 'conversion_error',
                        'python_formula': None,
                        'test_result': None,
                        'error': str(conversion_error)
                }
        
        return validation_results
