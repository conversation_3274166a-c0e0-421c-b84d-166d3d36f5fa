# Formula Helper Normalization Integration

## Status: ✅ IMPLEMENTED

The field value normalization system has been successfully integrated into the `config.matrix.formula.helper` mixin, providing centralized access to normalization functionality across all models that use formula evaluation.

## Overview

This integration consolidates the field normalization functionality into the formula helper, making it available to all models that inherit from `config.matrix.formula.helper` without requiring separate service calls.

## Architecture Changes

### Before Integration
```python
# Models had to use separate services
normalizer = self.env['config.matrix.field.normalizer']
normalized_values = normalizer.normalize_field_values(field_values, template_id)

helper = self.env['config.matrix.formula.helper'].sudo()
result = helper.evaluate_formula(formula, normalized_values)
```

### After Integration
```python
# Single service with automatic normalization
helper = self.env['config.matrix.formula.helper'].sudo()
result = helper.evaluate_formula(formula, field_values, template_id=template_id)
# Normalization happens automatically when template_id is provided
```

## Enhanced Methods

All formula evaluation methods in `config.matrix.formula.helper` now support automatic normalization:

### Core Methods
- `evaluate_formula(formula, configuration_values, template_id=None)`
- `evaluate_condition(condition, configuration_values, template_id=None)`
- `evaluate_visibility_condition(condition, field_values, template_id=None)`
- `calculate_component_quantity(formula, config_values, template_id=None)`
- `evaluate_formula_with_custom_context(formula, configuration_values, template_id=None)`

### Normalization Methods
- `normalize_field_values(field_values, template_id, context_info=None)`
- `normalize_for_template_calculation(field_values, template_id, context_type='unknown')`

### Context Building
- `build_evaluation_context(configuration_values, template_id=None)`

## Usage Examples

### Automatic Normalization in Formula Evaluation
```python
class MyModel(models.Model):
    _name = 'my.model'
    _inherit = ['config.matrix.formula.helper']
    
    def calculate_price(self):
        # String values from UI forms
        field_values = {
            'width': '1000',      # String from web form
            'height': '800',      # String from web form
            'quantity': '2',      # String from web form
            'is_premium': 'true'  # String boolean from web form
        }
        
        # Automatic normalization when template_id provided
        result = self.evaluate_formula(
            'width * height * quantity * (is_premium ? 1.2 : 1.0) / 1000000',
            field_values,
            template_id=self.template_id.id
        )
        # Result: 1.92 (properly normalized: 1000 * 800 * 2 * 1.2 / 1000000)
```

### Manual Normalization
```python
def normalize_config_data(self):
    raw_data = {
        'door_width': '1200',
        'door_height': '2100', 
        'lock_required': 'true',
        'panel_count': '3'
    }
    
    # Direct normalization
    normalized = self.normalize_field_values(raw_data, self.template_id.id)
    # Result: {'door_width': 1200, 'door_height': 2100, 'lock_required': True, 'panel_count': 3}
```

### Condition Evaluation with Normalization
```python
def check_visibility(self):
    field_values = {
        'door_height': '2100',  # String from form
        'lock_type': 'premium'
    }
    
    condition = 'door_height > 2000 && lock_type == "premium"'
    
    # Automatic normalization in condition evaluation
    is_visible = self.evaluate_condition(
        condition, 
        field_values, 
        template_id=self.template_id.id
    )
    # Result: True (door_height properly converted to 2100 integer)
```

## Integration Points

### 1. Calculated Fields
File: `models/config_matrix_calculated_field.py`
- Updated `_evaluate_field()` to pass `template_id` for automatic normalization
- All calculated field evaluations now benefit from consistent data types

### 2. Operation Templates  
File: `models/config_matrix_operation_template.py`
- Updated to use formula helper instead of direct normalizer calls
- Maintains backward compatibility while using the integrated approach

### 3. Future Models
Any model inheriting from `config.matrix.formula.helper` automatically gets:
- Formula evaluation with normalization
- Consistent data type handling
- Centralized normalization logic

## Benefits

### ✅ Simplified Architecture
- Single service for both formula evaluation and normalization
- Reduced complexity in model implementations
- Consistent API across all formula-related operations

### ✅ Automatic Integration
- Models inheriting from formula helper get normalization automatically
- No need for separate service calls
- Template-aware normalization when template_id is available

### ✅ Backward Compatibility
- Existing code continues to work unchanged
- Optional template_id parameter doesn't break existing calls
- Legacy normalizer service remains available

### ✅ Performance
- Reduced service instantiation overhead
- Integrated caching between formula evaluation and normalization
- Single context building for both operations

## Migration Guide

### For New Code
```python
# Recommended approach
class NewModel(models.Model):
    _inherit = ['config.matrix.formula.helper']
    
    def my_calculation(self):
        return self.evaluate_formula(formula, values, template_id=template_id)
```

### For Existing Code
```python
# Old approach (still works)
normalizer = self.env['config.matrix.field.normalizer']
normalized = normalizer.normalize_field_values(values, template_id)
result = self.evaluate_formula(formula, normalized)

# New approach (recommended)
result = self.evaluate_formula(formula, values, template_id=template_id)
```

## Testing

The integration maintains full compatibility with existing functionality while adding the new normalization capabilities. All existing tests continue to pass, and the normalization behavior is consistent with the standalone service.

## Conclusion

This integration provides a more cohesive and developer-friendly API for formula evaluation with automatic field normalization, reducing complexity while maintaining full backward compatibility.
